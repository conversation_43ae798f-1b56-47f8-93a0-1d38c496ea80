2025-07-22 21:20:32.263 [main] INFO  c.studycards.StudycardsApplication - 
                [] [] [] [] [] Starting StudycardsApplication using Java 17.0.14 with PID 11320 (D:\studyCards\backend\target\classes started by User in D:\studyCards\backend)
2025-07-22 21:20:32.266 [main] DEBUG c.studycards.StudycardsApplication - 
                [] [] [] [] [] Running with Spring Boot v3.2.3, Spring v6.1.4
2025-07-22 21:20:32.266 [main] INFO  c.studycards.StudycardsApplication - 
                [] [] [] [] [] The following 1 profile is active: "dev"
2025-07-22 21:20:39.732 [main] INFO  o.a.coyote.http11.Http11NioProtocol - 
                [] [] [] [] [] Initializing ProtocolHandler ["http-nio-8082"]
2025-07-22 21:20:40.866 [main] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Initializing RateLimitingFilter with maxRequestsPerMinute=60, blockDurationMinutes=5, maxTrackedIps=10000
2025-07-22 21:20:42.835 [main] WARN  o.h.j.b.i.EntityManagerFactoryBuilderImpl - 
                [] [] [] [] [] HHH000059: Defining hibernate.transaction.flush_before_completion=true ignored in HEM
2025-07-22 21:20:55.680 [main] INFO  org.redisson.Version - 
                [] [] [] [] [] Redisson 3.27.1
2025-07-22 21:20:56.874 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 
                [] [] [] [] [] 1 connections initialized for localhost/127.0.0.1:6379
2025-07-22 21:20:56.994 [redisson-netty-1-20] INFO  o.r.connection.ConnectionsHolder - 
                [] [] [] [] [] 24 connections initialized for localhost/127.0.0.1:6379
2025-07-22 21:20:58.841 [main] INFO  c.s.config.PasswordEncoderConfig - 
                [] [] [] [] [] PasswordEncoder successfully configured with BCrypt strength: 12
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Initializing StudyCards Cache Configuration
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache Configuration:
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Default Cache: 30min expiry, 100 initial capacity, 1000 max size
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Short-lived Cache: 5min expiry, 50 initial capacity, 500 max size
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   CAPTCHA Cache: 10min expiry, 100 initial capacity, 1000 max size
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Long-lived Cache: 2h expiry, 20 initial capacity, 200 max size
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Statistics enabled: true
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Memory-aware sizing: true
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Memory threshold: 80%
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache monitoring initialized
2025-07-22 21:20:59.424 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache configuration initialized successfully
2025-07-22 21:20:59.446 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Initializing StudyCards primary cache manager with comprehensive validation [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.446 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Validating StudyCards system readiness [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.446 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards system readiness validation passed [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.446 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Creating StudyCards cache manager with timeout protection [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.446 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Creating StudyCards default cache configuration [caffeine-config-170580259864700]
2025-07-22 21:20:59.446 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache configuration validation passed
2025-07-22 21:20:59.474 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache statistics enabled for default cache [caffeine-config-170580259864700]
2025-07-22 21:20:59.474 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards default cache configuration created successfully: expiry=30min, capacity=100, maxSize=1000 [caffeine-config-170580259864700]
2025-07-22 21:20:59.505 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache manager created with empty cache names (expected) [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.506 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache manager created successfully [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.506 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Configuring StudyCards-specific caches [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.593 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards caches configured successfully: [userFavorites, enhancedDeckResponses, publicStatistics, trendingDecks, studyStreaks, userNotifications, deckCards, tokenBlacklist, publicDecks, notificationPreferences, conceptExtraction, semanticSimilarity, unreadNotificationsCount, decksByTag, newDecks, userStatistics, enhancedNotifications, userSubscriptionStatus, semanticAnalysis] [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.593 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Initializing StudyCards cache monitoring [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.593 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache monitoring initialized for 19 caches [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.593 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Validating StudyCards cache manager health [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.605 [ForkJoinPool.commonPool-worker-1] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards default cache entry removed: key=studycards-health-test-170580406214500, cause=EXPLICIT, size=42bytes
2025-07-22 21:20:59.605 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache manager health validation completed successfully [studycards-cache-manager-170580257572400]
2025-07-22 21:20:59.605 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards primary cache manager initialized successfully with 19 caches [studycards-cache-manager-170580257572400]
2025-07-22 21:21:00.725 [main] ERROR o.s.b.w.e.tomcat.TomcatStarter - 
                [] [] [] [] [] Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'webSecurityConfig': Unsatisfied dependency expressed through field 'oAuth2AuthenticationSuccessHandler': Error creating bean with name 'OAuth2AuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'emailVerificationService': Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
2025-07-22 21:21:00.802 [ForkJoinPool.commonPool-worker-1] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache: Entry explicitly removed - likely cache invalidation
2025-07-22 21:21:00.863 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [RateLimitingFilter-Cleanup] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1679)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.865 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [mssql-jdbc-shared-timer-core-0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1170)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.866 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [HikariPool-1 housekeeper] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1679)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.868 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [Thread-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 java.base@17.0.14/sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:176)
2025-07-22 21:21:00.868 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:141)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:62)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:883)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.869 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-2] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.870 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.872 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.879 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-timer-4-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/java.lang.Thread.sleep(Native Method)
 app//io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:591)
 app//io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:487)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.911 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.911 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-6] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.912 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-7] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.912 [StudyCards-Async-1] DEBUG c.s.s.StudyCardsMonitoringService - 
                [recordMetric] [] [] [] [] StudyCards Metric [cache]: entry_removed = 1 - Tags: {cause=EXPLICIT, cacheType=default}
2025-07-22 21:21:00.913 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.913 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-8] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.915 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-9] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.915 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-2] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.916 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-10] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.917 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-11] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.918 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.927 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-12] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.927 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-13] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.928 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.928 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-14] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.929 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-15] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.929 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.930 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-16] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.930 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-17] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.931 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-6] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.931 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-18] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.931 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-19] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.931 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-7] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.932 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-20] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.932 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-21] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.933 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-8] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.933 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-22] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.933 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-23] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.933 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-9] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.935 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-24] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.935 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-25] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.936 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-10] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.936 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-26] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.936 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-27] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.936 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-11] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.937 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-28] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.937 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-29] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.937 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-12] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.938 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-30] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.938 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-31] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.938 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-13] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.939 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-32] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.939 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-14] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.939 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-15] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.940 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-16] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:21:00.973 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - 
                [] [] [] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server
2025-07-22 21:21:00.973 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Shutting down cache configuration
2025-07-22 21:21:00.975 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] === Cache Statistics Report ===
2025-07-22 21:21:00.975 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Total cache operations: 1
2025-07-22 21:21:00.975 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Total cache errors: 0
2025-07-22 21:21:00.985 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Memory usage: ٥٫٦٠%
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache semanticAnalysis: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache enhancedNotifications: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache semanticSimilarity: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache unreadNotificationsCount: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache conceptExtraction: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userFavorites: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache longLived: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userNotifications: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache publicStatistics: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache decksByTag: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache default: 0 hits, 0 misses, 1 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userSubscriptionStatus: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache enhancedDeckResponses: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache captcha: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.986 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache deckCards: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache studyStreaks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache shortLived: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache tokenBlacklist: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache notificationPreferences: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userStatistics: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache trendingDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache publicDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache newDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] === End Cache Statistics ===
2025-07-22 21:21:00.987 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache configuration shutdown completed
2025-07-22 21:21:01.022 [main] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Shutting down RateLimitingFilter scheduler
2025-07-22 21:21:01.081 [main] ERROR o.s.boot.SpringApplication - 
                [] [] [] [] [] Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:618)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.studycards.StudycardsApplication.main(StudycardsApplication.java:19)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:145)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:105)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:499)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:218)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webSecurityConfig': Unsatisfied dependency expressed through field 'oAuth2AuthenticationSuccessHandler': Error creating bean with name 'OAuth2AuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'emailVerificationService': Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:210)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:173)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:168)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:153)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4866)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:845)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:240)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:126)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OAuth2AuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'emailVerificationService': Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 66 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 80 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 94 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 108 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 122 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 132 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111)
	... 144 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'averageDifficulty' of 'com.studycards.model.Deck' [SELECT DISTINCT d FROM Deck d LEFT JOIN FETCH d.deckTags dt LEFT JOIN FETCH d.creator c LEFT JOIN d.favoriteBy f WHERE d.deleted = :includeDeleted AND (:isPublic IS NULL OR d.isPublic = :isPublic) AND (:creatorId IS NULL OR d.creator.id = :creatorId) AND (:query IS NULL OR     LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR     LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR     EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName IN :tagNames)) AND (:minDifficulty IS NULL OR d.averageDifficulty >= :minDifficulty) AND (:maxDifficulty IS NULL OR d.averageDifficulty <= :maxDifficulty) AND (:minCardCount IS NULL OR d.cardCount >= :minCardCount) AND (:maxCardCount IS NULL OR d.cardCount <= :maxCardCount) AND (:isFolder IS NULL OR d.isFolder = :isFolder) AND (:parentFolderId IS NULL OR d.parentFolder.id = :parentFolderId) AND (:isCollaborative IS NULL OR     (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR     (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) AND (:createdAfter IS NULL OR CAST(d.createdAt AS date) >= :createdAfter) AND (:createdBefore IS NULL OR CAST(d.createdAt AS date) <= :createdBefore) AND (:updatedAfter IS NULL OR CAST(d.updatedAt AS date) >= :updatedAfter) AND (:updatedBefore IS NULL OR CAST(d.updatedAt AS date) <= :updatedBefore) AND (:favoritesOnly = false OR f.id = :currentUserId) AND (:includeExpiredCreators = true OR c.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')) AND (d.creator.id = :currentUserId OR d.isPublic = true OR      EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :currentUserId))]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:848)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:753)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor32.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy189.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 150 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'averageDifficulty' of 'com.studycards.model.Deck' [SELECT DISTINCT d FROM Deck d LEFT JOIN FETCH d.deckTags dt LEFT JOIN FETCH d.creator c LEFT JOIN d.favoriteBy f WHERE d.deleted = :includeDeleted AND (:isPublic IS NULL OR d.isPublic = :isPublic) AND (:creatorId IS NULL OR d.creator.id = :creatorId) AND (:query IS NULL OR     LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR     LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR     EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName IN :tagNames)) AND (:minDifficulty IS NULL OR d.averageDifficulty >= :minDifficulty) AND (:maxDifficulty IS NULL OR d.averageDifficulty <= :maxDifficulty) AND (:minCardCount IS NULL OR d.cardCount >= :minCardCount) AND (:maxCardCount IS NULL OR d.cardCount <= :maxCardCount) AND (:isFolder IS NULL OR d.isFolder = :isFolder) AND (:parentFolderId IS NULL OR d.parentFolder.id = :parentFolderId) AND (:isCollaborative IS NULL OR     (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR     (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) AND (:createdAfter IS NULL OR CAST(d.createdAt AS date) >= :createdAfter) AND (:createdBefore IS NULL OR CAST(d.createdAt AS date) <= :createdBefore) AND (:updatedAfter IS NULL OR CAST(d.updatedAt AS date) >= :updatedAfter) AND (:updatedBefore IS NULL OR CAST(d.updatedAt AS date) <= :updatedBefore) AND (:favoritesOnly = false OR f.id = :currentUserId) AND (:includeExpiredCreators = true OR c.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')) AND (d.creator.id = :currentUserId OR d.isPublic = true OR      EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :currentUserId))]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:790)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:840)
	... 158 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'averageDifficulty' of 'com.studycards.model.Deck'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:202)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmFrom.resolvePathPart(AbstractSqmFrom.java:196)
	at org.hibernate.query.hql.internal.DomainPathPart.resolvePathPart(DomainPathPart.java:42)
	at org.hibernate.query.hql.internal.BasicDotIdentifierConsumer.consumeIdentifier(BasicDotIdentifierConsumer.java:91)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5060)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:5009)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4984)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathExpression(SemanticQueryBuilder.java:1776)
	at org.hibernate.grammars.hql.HqlParser$GeneralPathExpressionContext.accept(HqlParser.java:7699)
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46)
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitBarePrimaryExpression(HqlParserBaseVisitor.java:756)
	at org.hibernate.grammars.hql.HqlParser$BarePrimaryExpressionContext.accept(HqlParser.java:7157)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.createComparisonPredicate(SemanticQueryBuilder.java:2429)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:2392)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$ComparisonPredicateContext.accept(HqlParser.java:6164)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitOrPredicate(SemanticQueryBuilder.java:2271)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitOrPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$OrPredicateContext.accept(HqlParser.java:6255)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGroupedPredicate(SemanticQueryBuilder.java:2252)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGroupedPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$GroupedPredicateContext.accept(HqlParser.java:6084)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2262)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:2244)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$WhereClauseContext.accept(HqlParser.java:5905)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1159)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:941)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1869)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:926)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1740)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:443)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:402)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:311)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	... 162 common frames omitted
2025-07-22 21:29:07.227 [main] INFO  c.studycards.StudycardsApplication - 
                [] [] [] [] [] Starting StudycardsApplication using Java 17.0.14 with PID 32972 (D:\studyCards\backend\target\classes started by User in D:\studyCards\backend)
2025-07-22 21:29:07.229 [main] DEBUG c.studycards.StudycardsApplication - 
                [] [] [] [] [] Running with Spring Boot v3.2.3, Spring v6.1.4
2025-07-22 21:29:07.230 [main] INFO  c.studycards.StudycardsApplication - 
                [] [] [] [] [] The following 1 profile is active: "dev"
2025-07-22 21:29:12.555 [main] INFO  o.a.coyote.http11.Http11NioProtocol - 
                [] [] [] [] [] Initializing ProtocolHandler ["http-nio-8082"]
2025-07-22 21:29:13.300 [main] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Initializing RateLimitingFilter with maxRequestsPerMinute=60, blockDurationMinutes=5, maxTrackedIps=10000
2025-07-22 21:29:14.791 [main] WARN  o.h.j.b.i.EntityManagerFactoryBuilderImpl - 
                [] [] [] [] [] HHH000059: Defining hibernate.transaction.flush_before_completion=true ignored in HEM
2025-07-22 21:29:23.830 [main] INFO  org.redisson.Version - 
                [] [] [] [] [] Redisson 3.27.1
2025-07-22 21:29:24.264 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 
                [] [] [] [] [] 1 connections initialized for localhost/127.0.0.1:6379
2025-07-22 21:29:24.371 [redisson-netty-1-20] INFO  o.r.connection.ConnectionsHolder - 
                [] [] [] [] [] 24 connections initialized for localhost/127.0.0.1:6379
2025-07-22 21:29:25.666 [main] INFO  c.s.config.PasswordEncoderConfig - 
                [] [] [] [] [] PasswordEncoder successfully configured with BCrypt strength: 12
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Initializing StudyCards Cache Configuration
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache Configuration:
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Default Cache: 30min expiry, 100 initial capacity, 1000 max size
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Short-lived Cache: 5min expiry, 50 initial capacity, 500 max size
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   CAPTCHA Cache: 10min expiry, 100 initial capacity, 1000 max size
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Long-lived Cache: 2h expiry, 20 initial capacity, 200 max size
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Statistics enabled: true
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Memory-aware sizing: true
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] []   Memory threshold: 80%
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache monitoring initialized
2025-07-22 21:29:26.107 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache configuration initialized successfully
2025-07-22 21:29:26.128 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Initializing StudyCards primary cache manager with comprehensive validation [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.129 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Validating StudyCards system readiness [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.129 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards system readiness validation passed [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.129 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Creating StudyCards cache manager with timeout protection [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.129 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Creating StudyCards default cache configuration [caffeine-config-171086928894000]
2025-07-22 21:29:26.129 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache configuration validation passed
2025-07-22 21:29:26.137 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache statistics enabled for default cache [caffeine-config-171086928894000]
2025-07-22 21:29:26.137 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards default cache configuration created successfully: expiry=30min, capacity=100, maxSize=1000 [caffeine-config-171086928894000]
2025-07-22 21:29:26.166 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache manager created with empty cache names (expected) [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.166 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache manager created successfully [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.166 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Configuring StudyCards-specific caches [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.208 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards caches configured successfully: [userFavorites, enhancedDeckResponses, publicStatistics, trendingDecks, studyStreaks, userNotifications, deckCards, tokenBlacklist, publicDecks, notificationPreferences, conceptExtraction, semanticSimilarity, unreadNotificationsCount, decksByTag, newDecks, userStatistics, enhancedNotifications, userSubscriptionStatus, semanticAnalysis] [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.208 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Initializing StudyCards cache monitoring [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.209 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache monitoring initialized for 19 caches [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.209 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] Validating StudyCards cache manager health [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.230 [main] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache manager health validation completed successfully [studycards-cache-manager-171086926771600]
2025-07-22 21:29:26.230 [ForkJoinPool.commonPool-worker-1] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards default cache entry removed: key=studycards-health-test-171087008208400, cause=EXPLICIT, size=42bytes
2025-07-22 21:29:26.230 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards primary cache manager initialized successfully with 19 caches [studycards-cache-manager-171086926771600]
2025-07-22 21:29:27.565 [main] ERROR o.s.b.w.e.tomcat.TomcatStarter - 
                [] [] [] [] [] Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'webSecurityConfig': Unsatisfied dependency expressed through field 'oAuth2AuthenticationSuccessHandler': Error creating bean with name 'OAuth2AuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'emailVerificationService': Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
2025-07-22 21:29:27.613 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [RateLimitingFilter-Cleanup] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1679)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.614 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [mssql-jdbc-shared-timer-core-0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1170)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.615 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [HikariPool-1 housekeeper] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1679)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.14/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.615 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [Thread-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 java.base@17.0.14/sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:176)
2025-07-22 21:29:27.617 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:141)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:62)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:883)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.618 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-2] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-timer-4-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/java.lang.Thread.sleep(Native Method)
 app//io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:591)
 app//io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:487)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [ForkJoinPool.commonPool-worker-1] DEBUG com.studycards.config.CacheConfig - 
                [] [] [] [] [] StudyCards cache: Entry explicitly removed - likely cache invalidation
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-6] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-7] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-8] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-9] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.619 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-2] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.629 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-10] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.629 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-11] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.629 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.633 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-12] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.633 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-13] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.635 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.636 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-14] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.638 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-15] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.639 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.640 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-16] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.642 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-17] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.642 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-6] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.642 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-18] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.643 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-19] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.643 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-7] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.644 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-20] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.644 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-21] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.645 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-8] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.646 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-22] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.646 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-23] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.646 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-9] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.648 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-24] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.649 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-25] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.649 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-10] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.650 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-26] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.651 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-27] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.653 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-11] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.654 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-28] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.655 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-29] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.655 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-12] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.656 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-30] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.657 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-31] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.657 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-13] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.658 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-netty-1-32] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.nio.ch.WEPoll.wait(Native Method)
 java.base@17.0.14/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:111)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:129)
 java.base@17.0.14/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:146)
 app//io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 app//io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
 app//io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
 app//io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
 app//io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.659 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-14] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.659 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-15] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.660 [main] WARN  o.a.c.loader.WebappClassLoaderBase - 
                [] [] [] [] [] The web application [ROOT] appears to have started a thread named [redisson-3-16] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.14/java.util.concurrent.locks.LockSupport.park(LockSupport.java:341)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:506)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3465)
 java.base@17.0.14/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3436)
 java.base@17.0.14/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1630)
 java.base@17.0.14/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.14/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 app//io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@17.0.14/java.lang.Thread.run(Thread.java:840)
2025-07-22 21:29:27.665 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - 
                [] [] [] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server
2025-07-22 21:29:27.686 [StudyCards-Async-1] DEBUG c.s.s.StudyCardsMonitoringService - 
                [recordMetric] [] [] [] [] StudyCards Metric [cache]: entry_removed = 1 - Tags: {cause=EXPLICIT, cacheType=default}
2025-07-22 21:29:27.687 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Shutting down cache configuration
2025-07-22 21:29:27.687 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] === Cache Statistics Report ===
2025-07-22 21:29:27.687 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Total cache operations: 1
2025-07-22 21:29:27.687 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Total cache errors: 0
2025-07-22 21:29:27.689 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Memory usage: ٥٫٥٤%
2025-07-22 21:29:27.691 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache semanticAnalysis: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.691 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache enhancedNotifications: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.691 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache semanticSimilarity: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache unreadNotificationsCount: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache conceptExtraction: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userFavorites: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache longLived: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userNotifications: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache publicStatistics: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache decksByTag: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache default: 0 hits, 0 misses, 1 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userSubscriptionStatus: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache enhancedDeckResponses: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache captcha: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache deckCards: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache studyStreaks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache shortLived: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache tokenBlacklist: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache notificationPreferences: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.692 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userStatistics: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.693 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache trendingDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.693 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache publicDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.693 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache newDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-22 21:29:27.693 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] === End Cache Statistics ===
2025-07-22 21:29:27.693 [main] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache configuration shutdown completed
2025-07-22 21:29:27.730 [main] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Shutting down RateLimitingFilter scheduler
2025-07-22 21:29:27.796 [main] ERROR o.s.boot.SpringApplication - 
                [] [] [] [] [] Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:618)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.studycards.StudycardsApplication.main(StudycardsApplication.java:19)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:145)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:105)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:499)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:218)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webSecurityConfig': Unsatisfied dependency expressed through field 'oAuth2AuthenticationSuccessHandler': Error creating bean with name 'OAuth2AuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'emailVerificationService': Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:210)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:173)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:168)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:153)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4866)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:845)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:240)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:437)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:126)
	... 13 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OAuth2AuthenticationSuccessHandler': Unsatisfied dependency expressed through field 'emailVerificationService': Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 66 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'emailVerificationService': Unsatisfied dependency expressed through field 'subscriptionStatusService': Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 80 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'subscriptionStatusService': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 94 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userService': Unsatisfied dependency expressed through field 'deckRepository': Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 108 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'deckRepository' defined in com.studycards.repository.DeckRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 122 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable); Reason: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782)
	... 132 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract org.springframework.data.domain.Page com.studycards.repository.DeckRepository.unifiedAdvancedSearch(java.lang.String,java.lang.Boolean,java.lang.Long,java.util.List,boolean,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,boolean,java.lang.Boolean,java.lang.Long,java.lang.Boolean,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,java.time.LocalDate,boolean,boolean,java.lang.Long,org.springframework.data.domain.Pageable)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111)
	... 144 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'cardCount' of 'com.studycards.model.Deck' [SELECT DISTINCT d FROM Deck d LEFT JOIN FETCH d.deckTags dt LEFT JOIN FETCH d.creator c LEFT JOIN d.favoriteBy f WHERE d.deleted = :includeDeleted AND (:isPublic IS NULL OR d.isPublic = :isPublic) AND (:creatorId IS NULL OR d.creator.id = :creatorId) AND (:query IS NULL OR     LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR     LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR     EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName IN :tagNames)) AND (:minDifficulty IS NULL OR     (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) >= :minDifficulty) AND (:maxDifficulty IS NULL OR     (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) <= :maxDifficulty) AND (:minCardCount IS NULL OR d.cardCount >= :minCardCount) AND (:maxCardCount IS NULL OR d.cardCount <= :maxCardCount) AND (:isFolder IS NULL OR d.isFolder = :isFolder) AND (:parentFolderId IS NULL OR d.parentFolder.id = :parentFolderId) AND (:isCollaborative IS NULL OR     (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR     (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) AND (:createdAfter IS NULL OR CAST(d.createdAt AS date) >= :createdAfter) AND (:createdBefore IS NULL OR CAST(d.createdAt AS date) <= :createdBefore) AND (:updatedAfter IS NULL OR CAST(d.updatedAt AS date) >= :updatedAfter) AND (:updatedBefore IS NULL OR CAST(d.updatedAt AS date) <= :updatedBefore) AND (:favoritesOnly = false OR f.id = :currentUserId) AND (:includeExpiredCreators = true OR c.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')) AND (d.creator.id = :currentUserId OR d.isPublic = true OR      EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :currentUserId))]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:848)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:753)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor32.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy189.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 150 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'cardCount' of 'com.studycards.model.Deck' [SELECT DISTINCT d FROM Deck d LEFT JOIN FETCH d.deckTags dt LEFT JOIN FETCH d.creator c LEFT JOIN d.favoriteBy f WHERE d.deleted = :includeDeleted AND (:isPublic IS NULL OR d.isPublic = :isPublic) AND (:creatorId IS NULL OR d.creator.id = :creatorId) AND (:query IS NULL OR     LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR     LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR     EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName IN :tagNames)) AND (:minDifficulty IS NULL OR     (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) >= :minDifficulty) AND (:maxDifficulty IS NULL OR     (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) <= :maxDifficulty) AND (:minCardCount IS NULL OR d.cardCount >= :minCardCount) AND (:maxCardCount IS NULL OR d.cardCount <= :maxCardCount) AND (:isFolder IS NULL OR d.isFolder = :isFolder) AND (:parentFolderId IS NULL OR d.parentFolder.id = :parentFolderId) AND (:isCollaborative IS NULL OR     (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR     (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) AND (:createdAfter IS NULL OR CAST(d.createdAt AS date) >= :createdAfter) AND (:createdBefore IS NULL OR CAST(d.createdAt AS date) <= :createdBefore) AND (:updatedAfter IS NULL OR CAST(d.updatedAt AS date) >= :updatedAfter) AND (:updatedBefore IS NULL OR CAST(d.updatedAt AS date) <= :updatedBefore) AND (:favoritesOnly = false OR f.id = :currentUserId) AND (:includeExpiredCreators = true OR c.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')) AND (d.creator.id = :currentUserId OR d.isPublic = true OR      EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :currentUserId))]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:790)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:840)
	... 158 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'cardCount' of 'com.studycards.model.Deck'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:202)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmFrom.resolvePathPart(AbstractSqmFrom.java:196)
	at org.hibernate.query.hql.internal.DomainPathPart.resolvePathPart(DomainPathPart.java:42)
	at org.hibernate.query.hql.internal.BasicDotIdentifierConsumer.consumeIdentifier(BasicDotIdentifierConsumer.java:91)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5060)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:5009)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4984)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathExpression(SemanticQueryBuilder.java:1776)
	at org.hibernate.grammars.hql.HqlParser$GeneralPathExpressionContext.accept(HqlParser.java:7699)
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46)
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitBarePrimaryExpression(HqlParserBaseVisitor.java:756)
	at org.hibernate.grammars.hql.HqlParser$BarePrimaryExpressionContext.accept(HqlParser.java:7157)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.createComparisonPredicate(SemanticQueryBuilder.java:2429)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:2392)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$ComparisonPredicateContext.accept(HqlParser.java:6164)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitOrPredicate(SemanticQueryBuilder.java:2271)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitOrPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$OrPredicateContext.accept(HqlParser.java:6255)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGroupedPredicate(SemanticQueryBuilder.java:2252)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGroupedPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$GroupedPredicateContext.accept(HqlParser.java:6084)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2262)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2261)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:6039)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:2244)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$WhereClauseContext.accept(HqlParser.java:5905)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1159)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:941)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1869)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:926)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:269)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1740)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:443)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:402)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:311)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	... 162 common frames omitted
